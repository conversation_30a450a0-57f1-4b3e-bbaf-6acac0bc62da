<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?? 'الإيرادات - لوحة تحكم الأدمن' ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-sky-blue: #87CEEB;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --border-color: #E0E0E0;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-black);
        }

        .sidebar {
            min-height: 100vh;
            background: var(--admin-gradient);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .admin-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .stats-card {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            text-align: center;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .revenue-item {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .revenue-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    لوحة تحكم الأدمن
                </h4>
                <small class="text-white-50">نظام إدارة العيادات</small>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php" data-page="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="clinics.php" data-page="clinics">
                    <i class="fas fa-hospital me-2"></i>
                    العيادات
                </a>
                <a class="nav-link" href="subscriptions.php" data-page="subscriptions">
                    <i class="fas fa-credit-card me-2"></i>
                    الاشتراكات
                </a>
                <a class="nav-link" href="users.php" data-page="users">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
                <a class="nav-link" href="revenue.php" data-page="revenue">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات
                </a>
                <a class="nav-link" href="messages.php" data-page="messages">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php" data-page="settings">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </nav>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-chart-line me-3"></i>
                        الإيرادات
                    </h1>
                    <p class="mb-0 mt-2">مراقبة وتحليل إيرادات النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-dollar-sign" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <div class="stats-number">$<?= number_format($data['total_revenue'] ?? 0, 2) ?></div>
                    <div>إجمالي الإيرادات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <div class="stats-number">$<?= number_format(($data['monthly_revenue'] ?? 0), 2) ?></div>
                    <div>إيرادات الشهر الحالي</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-percentage" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <div class="stats-number">0%</div>
                    <div>نمو الإيرادات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <div class="stats-number"><?= isset($data['active_subscriptions']) ? $data['active_subscriptions'] : 0 ?></div>
                    <div>الاشتراكات النشطة</div>
                </div>
            </div>
        </div>

        <!-- Revenue Chart -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    تحليل الإيرادات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Revenue Details -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            تفاصيل الإيرادات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="empty-state">
                            <i class="fas fa-chart-line"></i>
                            <h5>لا توجد بيانات إيرادات</h5>
                            <p>سيتم عرض تفاصيل الإيرادات هنا عند توفر البيانات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-pie-chart me-2"></i>
                            توزيع الإيرادات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="pieChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Plans -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    خطط الاشتراك
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="revenue-item text-center">
                            <i class="fas fa-crown text-warning" style="font-size: 3rem; margin-bottom: 15px;"></i>
                            <h5>الخطة الاحترافية</h5>
                            <h3 class="text-success">$99</h3>
                            <p class="text-muted">شهرياً</p>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-success" style="width: 0%"></div>
                            </div>
                            <p class="mb-0"><strong>0%</strong> من إجمالي الإيرادات</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="revenue-item text-center">
                            <i class="fas fa-medal text-primary" style="font-size: 3rem; margin-bottom: 15px;"></i>
                            <h5>الخطة المتقدمة</h5>
                            <h3 class="text-primary">$59</h3>
                            <p class="text-muted">شهرياً</p>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-primary" style="width: 0%"></div>
                            </div>
                            <p class="mb-0"><strong>0%</strong> من إجمالي الإيرادات</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="revenue-item text-center">
                            <i class="fas fa-award text-info" style="font-size: 3rem; margin-bottom: 15px;"></i>
                            <h5>الخطة الأساسية</h5>
                            <h3 class="text-info">$29</h3>
                            <p class="text-muted">شهرياً</p>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-info" style="width: 0%"></div>
                            </div>
                            <p class="mb-0"><strong>0%</strong> من إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل القائمة الجانبية
        function activateNavLink() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                
                // استخراج اسم الملف من الرابط
                const fileName = href.split('/').pop();
                const currentFileName = currentPage.split('/').pop();
                
                // التحقق من تطابق اسم الملف
                if (fileName && fileName === currentFileName) {
                    link.classList.add('active');
                }
            });
        }

        // تبديل القائمة الجانبية (للموبايل)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // Revenue Chart - Empty Data
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات الشهرية',
                    data: [0, 0, 0, 0, 0, 0],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Pie Chart - Empty Data
        const pieCtx = document.getElementById('pieChart').getContext('2d');
        const pieChart = new Chart(pieCtx, {
            type: 'doughnut',
            data: {
                labels: ['الخطة الاحترافية', 'الخطة المتقدمة', 'الخطة الأساسية'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: [
                        '#ffc107',
                        '#007bff',
                        '#17a2b8'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        // تفعيل القائمة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            activateNavLink();
        });
    </script>
</body>
</html> 